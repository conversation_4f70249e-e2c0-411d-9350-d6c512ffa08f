import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  LinearProgress,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  People,
  School,
  WorkspacePremium,
  TrendingUp,
  CheckCircle,
  Star,
  Group
} from '@mui/icons-material';
import { analyticsService } from '../../firebase/adminServices';
import toast from 'react-hot-toast';

const DashboardOverview = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    overview: {
      totalStudents: 0,
      activeStudents: 0,
      totalCourses: 0,
      totalCertificates: 0,
      averageProgress: 0
    },
    students: {
      total: 0,
      active: 0,
      newThisWeek: 0,
      newThisMonth: 0,
      activeThisWeek: 0
    },
    courses: {
      total: 0,
      active: 0,
      totalEnrollments: 0,
      totalCompletions: 0,
      averageRating: 0,
      mostPopular: 'لا يوجد'
    },
    recentActivity: []
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const overviewStats = await analyticsService.getOverviewStats();
      const recentActivity = await analyticsService.getRecentActivity(5);

      setStats({
        overview: overviewStats.overview,
        students: overviewStats.students,
        courses: overviewStats.courses,
        recentActivity: recentActivity
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('فشل في تحميل بيانات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'منذ قليل';
    } else if (diffInHours < 24) {
      return `منذ ${Math.floor(diffInHours)} ساعة`;
    } else {
      return date.toLocaleDateString('ar-SA');
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'completion':
        return <CheckCircle color="success" />;
      case 'enrollment':
        return <School color="primary" />;
      case 'certificate':
        return <WorkspacePremium color="warning" />;
      case 'registration':
        return <People color="info" />;
      default:
        return <Star color="info" />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box sx={{ pr: { xs: 0, md: 4 }, maxWidth: '100%', overflow: 'hidden' }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', pr: { xs: 0, md: 2 } }}>
        لوحة التحكم الرئيسية
      </Typography>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4, pr: { xs: 0, md: 2 } }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#e3f2fd' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    إجمالي الطلاب
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                    {stats.overview.totalStudents}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +{stats.students.newThisWeek} هذا الأسبوع
                  </Typography>
                </Box>
                <People sx={{ fontSize: 40, color: '#1976d2' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#e8f5e8' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    الطلاب النشطون
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                    {stats.overview.activeStudents}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    {stats.students.activeThisWeek} نشط هذا الأسبوع
                  </Typography>
                </Box>
                <Group sx={{ fontSize: 40, color: '#4caf50' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#fff3e0' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    إجمالي الكورسات
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                    {stats.overview.totalCourses}
                  </Typography>
                  <Typography variant="body2" color="info.main">
                    {stats.courses.active} كورس نشط
                  </Typography>
                </Box>
                <School sx={{ fontSize: 40, color: '#ff9800' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#f3e5f5' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    الشهادات الصادرة
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
                    {stats.overview.totalCertificates}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    شهادة معتمدة
                  </Typography>
                </Box>
                <WorkspacePremium sx={{ fontSize: 40, color: '#9c27b0' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3} sx={{ pr: { xs: 0, md: 2 } }}>
        {/* Progress Overview */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                <TrendingUp sx={{ mr: 1 }} />
                نظرة عامة على التقدم
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">متوسط التقدم العام</Typography>
                  <Typography variant="body2">{stats.overview.averageProgress}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={stats.overview.averageProgress}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" color="primary.main" sx={{ fontWeight: 'bold' }}>
                      {stats.courses.totalEnrollments}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      إجمالي التسجيلات
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" color="success.main" sx={{ fontWeight: 'bold' }}>
                      {stats.courses.totalCompletions}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      الكورسات المكتملة
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" color="warning.main" sx={{ fontWeight: 'bold' }}>
                      {stats.courses.averageRating.toFixed(1)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      متوسط التقييم
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h5" color="info.main" sx={{ fontWeight: 'bold' }}>
                      {Math.round((stats.courses.totalCompletions / Math.max(stats.courses.totalEnrollments, 1)) * 100)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      معدل الإكمال
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3 }}>
                النشاط الأخير
              </Typography>

              {stats.recentActivity.length === 0 ? (
                <Alert severity="info">
                  لا توجد أنشطة حديثة. استخدم "إنشاء بيانات أولية" لإضافة بيانات تجريبية.
                </Alert>
              ) : (
                <List>
                  {stats.recentActivity.map((activity, index) => (
                    <ListItem key={index} sx={{ px: 0 }}>
                      <ListItemAvatar>
                        {getActivityIcon(activity.type)}
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                            {activity.student}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              {activity.action}
                            </Typography>
                            <Typography variant="caption" display="block" color="text.secondary">
                              {formatTime(activity.timestamp)}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mt: 2, pr: { xs: 0, md: 2 } }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                إحصائيات سريعة
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      الكورس الأكثر شعبية
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold', mt: 1 }}>
                      {stats.courses.mostPopular}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      طلاب جدد هذا الشهر
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold', mt: 1 }}>
                      {stats.students.newThisMonth}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      معدل النشاط
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold', mt: 1 }}>
                      {Math.round((stats.students.activeThisWeek / Math.max(stats.students.total, 1)) * 100)}%
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f5f5f5', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      حالة النظام
                    </Typography>
                    <Chip
                      label="يعمل بشكل طبيعي"
                      color="success"
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardOverview;
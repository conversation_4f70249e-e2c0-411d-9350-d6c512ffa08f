import {
  collection,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  limit,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from './config';
import { findStudentByCode } from './studentService';
import { logActivity } from './databaseService';

// ===== خدمة المصادقة الجديدة =====

/**
 * تسجيل دخول المدير
 */
export const loginAdmin = async (email, password) => {
  try {
    console.log('🔐 محاولة تسجيل دخول المدير...', email);

    // البحث عن المدير في قاعدة البيانات
    const adminQuery = query(
      collection(db, 'users'),
      where('email', '==', email),
      where('role', '==', 'admin'),
      limit(1)
    );

    const adminSnapshot = await getDocs(adminQuery);

    if (adminSnapshot.empty) {
      throw new Error('البريد الإلكتروني غير صحيح');
    }

    const adminDoc = adminSnapshot.docs[0];
    const adminData = adminDoc.data();

    // في النظام الحقيقي، يجب التحقق من كلمة المرور المشفرة
    // هنا سنستخدم كلمة مرور افتراضية للمدير
    if (password !== 'Admin123!') {
      throw new Error('كلمة المرور غير صحيحة');
    }

    // تحديث آخر تسجيل دخول
    const adminRef = doc(db, 'users', adminDoc.id);
    await updateDoc(adminRef, {
      lastLogin: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // تسجيل النشاط
    await logActivity(adminDoc.id, 'admin_login', {
      email,
      loginTime: new Date().toISOString()
    });

    const userData = {
      id: adminDoc.id,
      ...adminData,
      lastLogin: new Date()
    };

    console.log('✅ تم تسجيل دخول المدير بنجاح');
    return {
      success: true,
      user: userData
    };

  } catch (error) {
    console.error('❌ خطأ في تسجيل دخول المدير:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * تسجيل دخول الطالب بكود التسجيل
 */
export const loginStudent = async (studentCode) => {
  try {
    console.log('🔐 محاولة تسجيل دخول الطالب...', studentCode);

    // البحث عن الطالب بكود التسجيل
    const student = await findStudentByCode(studentCode);

    if (!student) {
      throw new Error('كود التسجيل غير صحيح');
    }

    if (!student.isActive) {
      throw new Error('حساب الطالب غير مفعل');
    }

    // تحديث آخر تسجيل دخول
    const studentRef = doc(db, 'users', student.id);
    await updateDoc(studentRef, {
      lastLogin: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // تسجيل النشاط
    await logActivity(student.id, 'student_login', {
      studentCode,
      loginTime: new Date().toISOString()
    });

    const userData = {
      ...student,
      lastLogin: new Date()
    };

    console.log('✅ تم تسجيل دخول الطالب بنجاح');
    return {
      success: true,
      user: userData
    };

  } catch (error) {
    console.error('❌ خطأ في تسجيل دخول الطالب:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * تحديث ملف المدير الشخصي
 */
export const updateAdminProfile = async (adminId, profileData) => {
  try {
    console.log('📝 تحديث ملف المدير الشخصي...', adminId);

    const adminRef = doc(db, 'users', adminId);
    
    const updateData = {
      ...profileData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(adminRef, updateData);

    // تسجيل النشاط
    await logActivity(adminId, 'profile_updated', {
      updatedFields: Object.keys(profileData)
    });

    console.log('✅ تم تحديث ملف المدير بنجاح');
    return {
      success: true,
      message: 'تم تحديث الملف الشخصي بنجاح'
    };

  } catch (error) {
    console.error('❌ خطأ في تحديث ملف المدير:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * تحديث ملف الطالب الشخصي
 */
export const updateStudentProfile = async (studentId, profileData) => {
  try {
    console.log('📝 تحديث ملف الطالب الشخصي...', studentId);

    const studentRef = doc(db, 'users', studentId);
    
    const updateData = {
      ...profileData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(studentRef, updateData);

    // تسجيل النشاط
    await logActivity(studentId, 'profile_updated', {
      updatedFields: Object.keys(profileData)
    });

    console.log('✅ تم تحديث ملف الطالب بنجاح');
    return {
      success: true,
      message: 'تم تحديث الملف الشخصي بنجاح'
    };

  } catch (error) {
    console.error('❌ خطأ في تحديث ملف الطالب:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * جلب ملف المستخدم الشخصي
 */
export const getUserProfile = async (userId) => {
  try {
    console.log('📥 جلب ملف المستخدم الشخصي...', userId);

    const userDoc = await getDoc(doc(db, 'users', userId));

    if (!userDoc.exists()) {
      throw new Error('المستخدم غير موجود');
    }

    const userData = {
      id: userDoc.id,
      ...userDoc.data(),
      createdAt: userDoc.data().createdAt?.toDate?.() || new Date(),
      updatedAt: userDoc.data().updatedAt?.toDate?.() || new Date(),
      lastLogin: userDoc.data().lastLogin?.toDate?.() || null
    };

    console.log('✅ تم جلب ملف المستخدم بنجاح');
    return userData;

  } catch (error) {
    console.error('❌ خطأ في جلب ملف المستخدم:', error);
    throw error;
  }
};

/**
 * التحقق من صحة الجلسة
 */
export const validateSession = async (userId, role) => {
  try {
    const user = await getUserProfile(userId);
    
    if (user.role !== role) {
      throw new Error('صلاحيات غير صحيحة');
    }

    if (role === 'student' && !user.isActive) {
      throw new Error('الحساب غير مفعل');
    }

    return {
      success: true,
      user
    };

  } catch (error) {
    console.error('❌ خطأ في التحقق من الجلسة:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * تسجيل الخروج
 */
export const logout = async (userId) => {
  try {
    // تسجيل النشاط
    await logActivity(userId, 'logout', {
      logoutTime: new Date().toISOString()
    });

    console.log('✅ تم تسجيل الخروج بنجاح');
    return {
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    };

  } catch (error) {
    console.error('❌ خطأ في تسجيل الخروج:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

export default {
  loginAdmin,
  loginStudent,
  updateAdminProfile,
  updateStudentProfile,
  getUserProfile,
  validateSession,
  logout
};

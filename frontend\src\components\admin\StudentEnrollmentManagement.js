import React, { useState, useEffect } from 'react';
import { useRealtimeUpdates } from '../../hooks/useRealtimeData';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  School as SchoolIcon,
  TrendingUp as ProgressIcon,
  CheckCircle as CompleteIcon
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import studentService from '../../firebase/studentService';
import courseService from '../../firebase/courseService';

const StudentEnrollmentManagement = () => {
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [enrollments, setEnrollments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('');

  // استخدام Real-time Updates للطلاب والكورسات
  const { courses: realtimeData, students: studentsData, isLoading } = useRealtimeUpdates();

  useEffect(() => {
    if (realtimeData.courses && realtimeData.courses.length > 0) {
      setCourses(realtimeData.courses);
    }
    if (studentsData.students && studentsData.students.length > 0) {
      setStudents(studentsData.students);
    }
    setLoading(isLoading);
  }, [realtimeData, studentsData, isLoading]);

  const fetchData = async () => {
    // لم تعد هناك حاجة لهذه الدالة مع Real-time Updates
    // ستبقى للتوافق مع الكود الموجود
    if (courses.length === 0 || students.length === 0) {
      try {
        setLoading(true);
        const [studentsData, coursesData] = await Promise.all([
          studentService.getAllStudents(),
          courseService.getAllCourses()
        ]);

        setStudents(studentsData);
        setCourses(coursesData);
      } catch (error) {
        console.error('خطأ في جلب البيانات:', error);
        toast.error('فشل في جلب البيانات');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleEnrollStudent = async () => {
    try {
      if (!selectedStudent || !selectedCourse) {
        toast.error('يرجى اختيار الطالب والكورس');
        return;
      }

      await studentService.enrollStudentInCourse(selectedStudent, selectedCourse, 'admin');
      
      toast.success('تم تسجيل الطالب في الكورس بنجاح');
      setOpenDialog(false);
      setSelectedStudent('');
      setSelectedCourse('');
      
      // تحديث البيانات
      fetchData();
    } catch (error) {
      console.error('خطأ في تسجيل الطالب:', error);
      toast.error(error.message || 'فشل في تسجيل الطالب');
    }
  };

  const handleUnenrollStudent = async (studentId, courseId) => {
    if (window.confirm('هل أنت متأكد من إلغاء تسجيل الطالب من هذا الكورس؟')) {
      try {
        await studentService.unenrollStudentFromCourse(studentId, courseId, 'admin');
        toast.success('تم إلغاء تسجيل الطالب من الكورس');
        fetchData();
      } catch (error) {
        console.error('خطأ في إلغاء التسجيل:', error);
        toast.error('فشل في إلغاء التسجيل');
      }
    }
  };

  const getStudentEnrollments = (studentId) => {
    return enrollments.filter(e => e.studentId === studentId);
  };

  const getCourseEnrollments = (courseId) => {
    return enrollments.filter(e => e.courseId === courseId);
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" component="h2">
              إدارة تسجيل الطلاب في الكورسات
            </Typography>
            <Button
              variant="contained"
              startIcon={<PersonAddIcon />}
              onClick={() => setOpenDialog(true)}
            >
              تسجيل طالب في كورس
            </Button>
          </Box>

          <Alert severity="info" sx={{ mb: 3 }}>
            يمكنك تسجيل الطلاب في الكورسات المختلفة وتتبع تقدمهم. سيتم تحديث إحصائيات الطلاب والكورسات تلقائياً.
          </Alert>

          <Grid container spacing={3}>
            {/* قائمة الطلاب وتسجيلاتهم */}
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                الطلاب المسجلون
              </Typography>
              <List>
                {students.map((student) => (
                  <ListItem key={student.id}>
                    <ListItemAvatar>
                      <Avatar>
                        <SchoolIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={student.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            كود التسجيل: {student.studentCode}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            الكورسات المسجل فيها: {student.enrolledCourses || 0}
                          </Typography>
                          <Box mt={1}>
                            <Chip
                              label={student.isActive ? 'مفعل' : 'غير مفعل'}
                              color={student.isActive ? 'success' : 'default'}
                              size="small"
                            />
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>

              {students.length === 0 && (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    لا توجد طلاب مسجلين
                  </Typography>
                </Box>
              )}
            </Grid>

            {/* قائمة الكورسات وعدد المسجلين */}
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                الكورسات المتاحة
              </Typography>
              <List>
                {courses.map((course) => (
                  <ListItem key={course.id}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <SchoolIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={course.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {course.category} • {course.level}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            الطلاب المسجلون: {course.enrolledStudents || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            عدد الفيديوهات: {course.totalVideos || 0}
                          </Typography>
                          <Box mt={1}>
                            <Chip
                              label={course.isPublished ? 'منشور' : 'غير منشور'}
                              color={course.isPublished ? 'success' : 'default'}
                              size="small"
                            />
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>

              {courses.length === 0 && (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    لا توجد كورسات متاحة
                  </Typography>
                </Box>
              )}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Dialog تسجيل طالب في كورس */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>تسجيل طالب في كورس</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>اختر الطالب</InputLabel>
                <Select
                  value={selectedStudent}
                  onChange={(e) => setSelectedStudent(e.target.value)}
                  label="اختر الطالب"
                >
                  {students.filter(s => s.isActive).map((student) => (
                    <MenuItem key={student.id} value={student.id}>
                      {student.name} ({student.studentCode})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>اختر الكورس</InputLabel>
                <Select
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                  label="اختر الكورس"
                >
                  {courses.filter(c => c.isPublished).map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.title} ({course.category})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>إلغاء</Button>
          <Button variant="contained" onClick={handleEnrollStudent}>
            تسجيل الطالب
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentEnrollmentManagement;

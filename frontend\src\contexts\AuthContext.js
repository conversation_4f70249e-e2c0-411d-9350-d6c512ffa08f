import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';
import { authServices } from '../firebase/services';
import { auth } from '../firebase/config';
import { initializeFirebaseData, testFirebaseConnection } from '../utils/initializeData';
import { seedFirebaseData } from '../utils/seedFirebase';
import { getUserProfile, ensureUserProfile } from '../firebase/profileService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Configure axios defaults
axios.defaults.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';

// Mock data for Firebase hosting (when no backend is available)
const mockData = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin123!',
    name: 'علاء عبد الحميد',
    role: 'admin',
    id: 'admin1'
  },
  students: [
    {
      studentCode: '123456',
      name: 'أحمد محمد علي',
      role: 'student',
      id: 'student1',
      isActive: true
    },
    {
      studentCode: '789012',
      name: 'فاطمة علي حسن',
      role: 'student',
      id: 'student2',
      isActive: true
    }
  ]
};

// Check if we're running on Firebase hosting (no backend available)
const isFirebaseHosting = window.location.hostname.includes('web.app') || window.location.hostname.includes('firebaseapp.com');

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Set up axios interceptor for auth token
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    // Response interceptor to handle token expiration
    const responseInterceptor = axios.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          logout();
          toast.error('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى');
        }
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  // Check if user is logged in on app start
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      // Initialize Firebase Auth state listener
      const unsubscribe = authServices.onAuthStateChanged(async (firebaseUser) => {
        if (firebaseUser) {
          // User is signed in with Firebase Auth
          try {
            const userDoc = await authServices.getUser(firebaseUser.uid);
            setUser(userDoc);
          } catch (error) {
            console.error('Error getting user data:', error);
            // Fallback to saved user data
            const savedUser = localStorage.getItem('user');
            if (savedUser) {
              try {
                const userData = JSON.parse(savedUser);
                setUser(userData);
              } catch (parseError) {
                console.error('Error parsing saved user:', parseError);
                setUser(null);
              }
            }
          }
        } else {
          // No Firebase user, check for saved session
          const savedUser = localStorage.getItem('user');
          if (savedUser && isFirebaseHosting) {
            try {
              const userData = JSON.parse(savedUser);
              setUser(userData);
            } catch (error) {
              console.error('Error parsing saved user:', error);
              localStorage.removeItem('user');
              setUser(null);
            }
          } else {
            setUser(null);
          }
        }
        setLoading(false);
      });

      // Test Firebase connection and initialize data
      try {
        const connectionTest = await testFirebaseConnection();
        if (connectionTest.success) {
          console.log('✅ Firebase متصل بنجاح');

          // Initialize default data
          const initResult = await initializeFirebaseData();
          if (initResult.success) {
            console.log('✅ تم تهيئة البيانات:', initResult.message);
          }

          // Create test data in Firebase
          try {
            await seedFirebaseData();
            console.log('✅ تم إنشاء البيانات التجريبية في Firebase');

            // إنشاء البيانات الحقيقية أيضاً
            const { createRealStudentData } = await import('../utils/createRealData');
            await createRealStudentData();
            console.log('✅ تم إنشاء البيانات الحقيقية للطلاب');
          } catch (seedError) {
            console.log('⚠️ خطأ في إنشاء البيانات التجريبية:', seedError.message);
          }
        }
      } catch (error) {
        console.log('⚠️ Firebase غير متاح، استخدام البيانات المحلية');
      }

      // Return cleanup function
      return unsubscribe;
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setLoading(false);
    }
  };

  const loginAdmin = async (email, password) => {
    try {
      // Try Firebase Authentication first
      const result = await authServices.loginAdmin(email, password);

      if (result.success) {
        setUser(result.user);
        localStorage.setItem('user', JSON.stringify(result.user));
        toast.success(`مرحباً ${result.user.name}!`);
        return { success: true };
      } else {
        // Fallback to mock authentication for demo
        if (isFirebaseHosting && email === mockData.admin.email && password === mockData.admin.password) {
          const userData = {
            id: mockData.admin.id,
            name: mockData.admin.name,
            email: mockData.admin.email,
            role: mockData.admin.role
          };

          localStorage.setItem('user', JSON.stringify(userData));
          setUser(userData);

          toast.success(`مرحباً ${userData.name}!`);
          return { success: true };
        } else {
          toast.error(result.message || 'بيانات دخول خاطئة');
          return { success: false, message: result.message };
        }
      }
    } catch (error) {
      // Fallback to original API call
      try {
        const response = await axios.post('/auth/admin/login', {
          email,
          password
        });

        const { token, user: userData } = response.data;

        localStorage.setItem('token', token);
        localStorage.setItem('user', JSON.stringify(userData));
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        setUser(userData);

        toast.success(`مرحباً ${userData.name}!`);
        return { success: true };
      } catch (apiError) {
        const message = apiError.response?.data?.message || error.message || 'خطأ في تسجيل الدخول';
        toast.error(message);
        return { success: false, message };
      }
    }
  };

  const loginStudent = async (code) => {
    try {
      // Try Firebase Authentication first
      const result = await authServices.loginStudent(code);

      if (result.success) {
        // جلب أو إنشاء الملف الشخصي من Firebase
        let userProfile = null;
        try {
          userProfile = await ensureUserProfile(result.user.id, {
            name: result.user.name,
            email: result.user.email || '',
            phone: result.user.phone || '',
            studentCode: result.user.studentCode,
            role: result.user.role || 'student'
          });
          console.log('✅ تم جلب الملف الشخصي من Firebase:', userProfile);
        } catch (profileError) {
          console.log('⚠️ فشل جلب الملف الشخصي، استخدام البيانات الأساسية:', profileError.message);
          userProfile = result.user;
        }

        // استخدام بيانات الملف الشخصي المحدثة
        const finalUser = { ...result.user, ...userProfile };
        setUser(finalUser);
        localStorage.setItem('user', JSON.stringify(finalUser));

        // تسجيل نشاط تسجيل الدخول
        try {
          const { logUserActivity, createDefaultActivities } = await import('../firebase/authService');
          await logUserActivity(finalUser.id, 'login', {
            description: 'تسجيل الدخول إلى المنصة',
            timestamp: new Date().toISOString()
          });

          // إنشاء نشاطات افتراضية للطلاب الجدد
          await createDefaultActivities(finalUser.id);
        } catch (activityError) {
          console.log('⚠️ فشل تسجيل النشاط:', activityError.message);
        }

        toast.success(`مرحباً ${finalUser.name}!`);
        return { success: true };
      } else {
        // Fallback to mock authentication for demo
        if (isFirebaseHosting) {
          const student = mockData.students.find(s => s.studentCode === code);
          if (student) {
            const userData = {
              id: student.id,
              name: student.name,
              studentCode: student.studentCode,
              role: student.role
            };

            localStorage.setItem('user', JSON.stringify(userData));
            setUser(userData);

            toast.success(`مرحباً ${userData.name}!`);
            return { success: true };
          } else {
            toast.error('كود الطالب غير صحيح');
            return { success: false, message: 'كود الطالب غير صحيح' };
          }
        } else {
          toast.error(result.message || 'كود الطالب غير صحيح');
          return { success: false, message: result.message };
        }
      }
    } catch (error) {
      // Fallback to original API call
      try {
        const response = await axios.post('/auth/student/login', {
          code
        });

        const { token, user: userData } = response.data;

        localStorage.setItem('token', token);
        localStorage.setItem('user', JSON.stringify(userData));
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        setUser(userData);

        toast.success(`مرحباً ${userData.name}!`);
        return { success: true };
      } catch (apiError) {
        const message = apiError.response?.data?.message || error.message || 'خطأ في تسجيل الدخول';
        toast.error(message);
        return { success: false, message };
      }
    }
  };

  const logout = async () => {
    try {
      // Try Firebase logout first
      await authServices.logout();
    } catch (error) {
      console.error('Firebase logout error:', error);
    }

    // Clear local storage and state
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    delete axios.defaults.headers.common['Authorization'];
    setUser(null);
    toast.success('تم تسجيل الخروج بنجاح');
  };

  const updateUser = async (userData) => {
    const updatedUser = { ...user, ...userData };
    setUser(updatedUser);

    // حفظ البيانات المحدثة في localStorage
    localStorage.setItem('user', JSON.stringify(updatedUser));

    // محاولة تحديث البيانات في Firebase أيضاً
    if (updatedUser.id) {
      try {
        const { updateUserProfile } = await import('../firebase/profileService');
        await updateUserProfile(updatedUser.id, {
          name: updatedUser.name,
          email: updatedUser.email,
          phone: updatedUser.phone,
          studentCode: updatedUser.studentCode
        });
        console.log('✅ تم تحديث البيانات في Firebase');
      } catch (error) {
        console.log('⚠️ فشل تحديث البيانات في Firebase:', error.message);
      }
    }
  };

  const value = {
    user,
    loading,
    loginAdmin,
    loginStudent,
    logout,
    updateUser,
    checkAuth
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

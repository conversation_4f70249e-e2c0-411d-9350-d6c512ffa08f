import React, { createContext, useContext, useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import authServiceNew from '../firebase/authServiceNew';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check if user is logged in on app start
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      // Initialize Firebase Auth state listener
      const unsubscribe = authServices.onAuthStateChanged(async (firebaseUser) => {
        if (firebaseUser) {
          // User is signed in with Firebase Auth
          try {
            const userDoc = await authServices.getUser(firebaseUser.uid);
            setUser(userDoc);
          } catch (error) {
            console.error('Error getting user data:', error);
            // Fallback to saved user data
            const savedUser = localStorage.getItem('user');
            if (savedUser) {
              try {
                const userData = JSON.parse(savedUser);
                setUser(userData);
              } catch (parseError) {
                console.error('Error parsing saved user:', parseError);
                setUser(null);
              }
            }
          }
        } else {
          // No Firebase user, check for saved session
          const savedUser = localStorage.getItem('user');
          if (savedUser && isFirebaseHosting) {
            try {
              const userData = JSON.parse(savedUser);
              setUser(userData);
            } catch (error) {
              console.error('Error parsing saved user:', error);
              localStorage.removeItem('user');
              setUser(null);
            }
          } else {
            setUser(null);
          }
        }
        setLoading(false);
      });

      // Test Firebase connection and initialize data
      try {
        const connectionTest = await testFirebaseConnection();
        if (connectionTest.success) {
          console.log('✅ Firebase متصل بنجاح');

          // Initialize default data
          const initResult = await initializeFirebaseData();
          if (initResult.success) {
            console.log('✅ تم تهيئة البيانات:', initResult.message);
          }

          // Create test data in Firebase
          try {
            await seedFirebaseData();
            console.log('✅ تم إنشاء البيانات التجريبية في Firebase');

            // إنشاء البيانات الحقيقية أيضاً
            const { createRealStudentData } = await import('../utils/createRealData');
            await createRealStudentData();
            console.log('✅ تم إنشاء البيانات الحقيقية للطلاب');
          } catch (seedError) {
            console.log('⚠️ خطأ في إنشاء البيانات التجريبية:', seedError.message);
          }
        }
      } catch (error) {
        console.log('⚠️ Firebase غير متاح، استخدام البيانات المحلية');
      }

      // Return cleanup function
      return unsubscribe;
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setLoading(false);
    }
  };

  const loginAdmin = async (email, password) => {
    try {
      // استخدام النظام الجديد لتسجيل دخول المدير
      const result = await authServiceNew.loginAdmin(email, password);

      if (result.success) {
        setUser(result.user);
        localStorage.setItem('user', JSON.stringify(result.user));
        toast.success(`مرحباً ${result.user.name}!`);
        return { success: true };
      } else {
        toast.error(result.message || 'فشل في تسجيل الدخول');
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('خطأ في تسجيل دخول المدير:', error);
      toast.error('خطأ في تسجيل الدخول');
      return { success: false, message: error.message };
    }
  };

  const loginStudent = async (code) => {
    try {
      // استخدام النظام الجديد لتسجيل دخول الطالب
      const result = await authServiceNew.loginStudent(code);

      if (result.success) {
        setUser(result.user);
        localStorage.setItem('user', JSON.stringify(result.user));
        toast.success(`مرحباً ${result.user.name}!`);
        return { success: true };
      } else {
        toast.error(result.message || 'كود التسجيل غير صحيح');
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('خطأ في تسجيل دخول الطالب:', error);
      toast.error('خطأ في تسجيل الدخول');
      return { success: false, message: error.message };
    }
  };

  const logout = async () => {
    try {
      if (user) {
        await authServiceNew.logout(user.id);
      }
    } catch (error) {
      console.log('خطأ في تسجيل الخروج:', error);
    }

    // Clear local storage and state
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    toast.success('تم تسجيل الخروج بنجاح');
  };

  const updateUser = async (userData) => {
    const updatedUser = { ...user, ...userData };
    setUser(updatedUser);

    // حفظ البيانات المحدثة في localStorage
    localStorage.setItem('user', JSON.stringify(updatedUser));

    // محاولة تحديث البيانات في Firebase أيضاً
    if (updatedUser.id) {
      try {
        const { updateUserProfile } = await import('../firebase/profileService');
        await updateUserProfile(updatedUser.id, {
          name: updatedUser.name,
          email: updatedUser.email,
          phone: updatedUser.phone,
          studentCode: updatedUser.studentCode
        });
        console.log('✅ تم تحديث البيانات في Firebase');
      } catch (error) {
        console.log('⚠️ فشل تحديث البيانات في Firebase:', error.message);
      }
    }
  };

  const value = {
    user,
    loading,
    loginAdmin,
    loginStudent,
    logout,
    updateUser,
    checkAuth
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

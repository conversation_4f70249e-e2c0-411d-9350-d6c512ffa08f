import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  ContentCopy,
  Block,
  CheckCircle
} from '@mui/icons-material';
import { studentManagementService } from '../../firebase/adminServices';
import toast from 'react-hot-toast';

const StudentManagement = () => {
  const [students, setStudents] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingStudent, setEditingStudent] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    isActive: true
  });

  useEffect(() => {
    fetchStudents();
  }, []);

  const fetchStudents = async () => {
    try {
      const studentsData = await studentManagementService.getAllStudents();
      setStudents(studentsData);
    } catch (error) {
      console.error('خطأ في جلب الطلاب:', error);
      toast.error('فشل في تحميل بيانات الطلاب');
    }
  };

  const handleOpenDialog = (student = null) => {
    if (student) {
      setEditingStudent(student);
      setFormData({
        name: student.name,
        email: student.email || '',
        phone: student.phone || '',
        isActive: student.isActive
      });
    } else {
      setEditingStudent(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        isActive: true
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingStudent(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      isActive: true
    });
  };

  const handleSubmit = async () => {
    try {
      if (editingStudent) {
        // تحديث طالب موجود
        await studentManagementService.updateStudent(editingStudent.id, formData);
        setStudents(students.map(student =>
          student.id === editingStudent.id ? { ...student, ...formData } : student
        ));
        toast.success('تم تحديث بيانات الطالب بنجاح');
      } else {
        // إضافة طالب جديد
        const studentData = {
          ...formData,
          studentCode: Math.floor(100000 + Math.random() * 900000).toString(),
          email: formData.email || '',
          phone: formData.phone || ''
        };

        const newStudentId = await studentManagementService.addStudent(studentData);
        const newStudent = {
          id: newStudentId,
          ...studentData,
          createdAt: new Date(),
          enrolledCourses: [],
          completedCourses: [],
          certificates: []
        };

        setStudents([...students, newStudent]);
        toast.success(`تم إنشاء الطالب بنجاح! كود الطالب: ${studentData.studentCode}`);
      }
      handleCloseDialog();
    } catch (error) {
      console.error('خطأ في حفظ الطالب:', error);
      toast.error('فشل في حفظ بيانات الطالب');
    }
  };

  const handleDelete = async (studentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
      try {
        await studentManagementService.deleteStudent(studentId);
        setStudents(students.filter(student => student.id !== studentId));
        toast.success('تم حذف الطالب بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الطالب:', error);
        toast.error('فشل في حذف الطالب');
      }
    }
  };

  const toggleStudentStatus = async (studentId, currentStatus) => {
    try {
      await studentManagementService.toggleStudentStatus(studentId, !currentStatus);
      setStudents(students.map(student =>
        student.id === studentId ? { ...student, isActive: !currentStatus } : student
      ));
      toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الطالب بنجاح`);
    } catch (error) {
      console.error('خطأ في تغيير حالة الطالب:', error);
      toast.error('فشل في تغيير حالة الطالب');
    }
  };

  const copyStudentCode = (code) => {
    navigator.clipboard.writeText(code);
    toast.success('تم نسخ كود الطالب');
  };

  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <Box sx={{ pr: { xs: 0, md: 4 }, maxWidth: '100%', overflow: 'hidden' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, pr: { xs: 0, md: 2 } }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الطلاب
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          sx={{ borderRadius: 2 }}
        >
          إضافة طالب جديد
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 4, pr: { xs: 0, md: 2 } }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#e3f2fd' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                {students.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                إجمالي الطلاب
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#e8f5e8' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                {students.filter(s => s.isActive).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                الطلاب النشطون
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#fff3e0' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                {students.reduce((total, student) => total + (student.enrolledCourses?.length || 0), 0)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                إجمالي التسجيلات
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#f3e5f5' }}>
            <CardContent>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
                {students.filter(s => {
                  const today = new Date().toDateString();
                  const lastLogin = s.lastLogin ? new Date(s.lastLogin).toDateString() : null;
                  return lastLogin === today;
                }).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                نشط اليوم
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* جدول الطلاب */}
      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: '#f5f5f5' }}>
              <TableCell sx={{ fontWeight: 'bold' }}>الطالب</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>كود الطالب</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الدورات المسجلة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>تاريخ الانضمام</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>آخر نشاط</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {students.map((student) => (
              <TableRow key={student.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ mr: 2, bgcolor: '#1976d2' }}>
                      {getInitials(student.name)}
                    </Avatar>
                    <Typography variant="subtitle2" sx={{ fontWeight: 'medium' }}>
                      {student.name}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', mr: 1 }}>
                      {student.studentCode}
                    </Typography>
                    <Tooltip title="نسخ الكود">
                      <IconButton
                        size="small"
                        onClick={() => copyStudentCode(student.studentCode)}
                      >
                        <ContentCopy fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {student.enrolledCourses?.length || 0} دورة
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {student.createdAt ? new Date(student.createdAt).toLocaleDateString('ar-SA') : 'غير محدد'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {student.lastLogin ? new Date(student.lastLogin).toLocaleDateString('ar-SA') : 'لم يسجل دخول'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={student.isActive ? <CheckCircle /> : <Block />}
                    label={student.isActive ? 'نشط' : 'غير نشط'}
                    color={student.isActive ? 'success' : 'error'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="تعديل">
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(student)}
                        sx={{ color: '#1976d2' }}
                      >
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={student.isActive ? 'إلغاء التفعيل' : 'تفعيل'}>
                      <IconButton
                        size="small"
                        onClick={() => toggleStudentStatus(student.id, student.isActive)}
                        sx={{ color: student.isActive ? '#ff9800' : '#4caf50' }}
                      >
                        {student.isActive ? <Block /> : <CheckCircle />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="حذف">
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(student.id)}
                        sx={{ color: '#d32f2f' }}
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog لإضافة/تعديل الطالب */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingStudent ? 'تعديل الطالب' : 'إضافة طالب جديد'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="اسم الطالب"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              sx={{ mb: 3 }}
              required
            />

            <TextField
              fullWidth
              label="البريد الإلكتروني"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              sx={{ mb: 3 }}
            />

            <TextField
              fullWidth
              label="رقم الهاتف"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              sx={{ mb: 3 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="طالب نشط"
            />
            
            {!editingStudent && (
              <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                سيتم إنشاء كود طالب مكون من 6 أرقام تلقائياً
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingStudent ? 'تحديث' : 'إضافة'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentManagement;

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Avatar,
  Grid,
  InputAdornment,
  Switch,
  FormControlLabel,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  Visibility,
  School,
  ContentCopy,
  CheckCircle,
  Cancel
} from '@mui/icons-material';
// import { DataGrid } from '@mui/x-data-grid';
import toast from 'react-hot-toast';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { studentManagementService } from '../../firebase/adminServices';

const StudentsManagement = () => {
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState({
    page: 0,
    pageSize: 10,
    total: 0
  });

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    isActive: true
  });

  useEffect(() => {
    fetchStudents();
  }, [pagination.page, pagination.pageSize, searchTerm]);

  const fetchStudents = async () => {
    setLoading(true);
    try {
      // استخدام Firebase Client SDK بدلاً من API
      const allStudents = await studentManagementService.getAllStudents();

      // تطبيق البحث محلياً
      let filteredStudents = allStudents;
      if (searchTerm) {
        filteredStudents = allStudents.filter(student =>
          student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          student.phone?.includes(searchTerm)
        );
      }

      setStudents(filteredStudents);
      setPagination(prev => ({
        ...prev,
        total: filteredStudents.length
      }));
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('خطأ في جلب بيانات الطلاب');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateStudent = async () => {
    try {
      const response = await axios.post('/admin/students', formData);
      
      toast.success('تم إنشاء الطالب بنجاح');
      setOpenDialog(false);
      resetForm();
      fetchStudents();
      
      // Show student code
      toast.success(`كود الطالب: ${response.data.student.studentCode}`, {
        duration: 10000
      });
    } catch (error) {
      console.error('Error creating student:', error);
      toast.error(error.response?.data?.message || 'خطأ في إنشاء الطالب');
    }
  };

  const handleUpdateStudent = async () => {
    try {
      await axios.put(`/admin/students/${selectedStudent._id}`, formData);
      
      toast.success('تم تحديث بيانات الطالب بنجاح');
      setOpenDialog(false);
      resetForm();
      fetchStudents();
    } catch (error) {
      console.error('Error updating student:', error);
      toast.error(error.response?.data?.message || 'خطأ في تحديث الطالب');
    }
  };

  const handleDeleteStudent = async (studentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
      try {
        await axios.delete(`/admin/students/${studentId}`);
        toast.success('تم حذف الطالب بنجاح');
        fetchStudents();
      } catch (error) {
        console.error('Error deleting student:', error);
        toast.error(error.response?.data?.message || 'خطأ في حذف الطالب');
      }
    }
  };

  const handleToggleStatus = async (student) => {
    try {
      await axios.put(`/admin/students/${student._id}`, {
        isActive: !student.isActive
      });
      
      toast.success(`تم ${!student.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الطالب`);
      fetchStudents();
    } catch (error) {
      console.error('Error toggling student status:', error);
      toast.error('خطأ في تغيير حالة الطالب');
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('تم نسخ الكود');
  };

  const resetForm = () => {
    setFormData({
      name: '',
      isActive: true
    });
    setSelectedStudent(null);
  };

  const openCreateDialog = () => {
    resetForm();
    setOpenDialog(true);
  };

  const openEditDialog = (student) => {
    setSelectedStudent(student);
    setFormData({
      name: student.name,
      isActive: student.isActive
    });
    setOpenDialog(true);
  };

  const columns = [
    {
      field: 'avatar',
      headerName: '',
      width: 60,
      renderCell: (params) => (
        <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
          {params.row.name.charAt(0)}
        </Avatar>
      ),
      sortable: false,
      filterable: false
    },
    {
      field: 'name',
      headerName: 'اسم الطالب',
      flex: 1,
      minWidth: 150
    },
    {
      field: 'studentCode',
      headerName: 'كود الطالب',
      width: 120,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
            {params.value}
          </Typography>
          <Tooltip title="نسخ الكود">
            <IconButton
              size="small"
              onClick={() => copyToClipboard(params.value)}
            >
              <ContentCopy fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    },
    {
      field: 'enrolledCourses',
      headerName: 'الكورسات',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.row.enrolledCourses?.length || 0}
          size="small"
          color="primary"
          variant="outlined"
        />
      )
    },
    {
      field: 'isActive',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          color={params.value ? 'success' : 'error'}
          size="small"
          icon={params.value ? <CheckCircle /> : <Cancel />}
        />
      )
    },
    {
      field: 'lastLogin',
      headerName: 'آخر دخول',
      width: 150,
      renderCell: (params) => (
        <Typography variant="body2" color="textSecondary">
          {params.value 
            ? format(new Date(params.value), 'dd/MM/yyyy', { locale: ar })
            : 'لم يدخل بعد'
          }
        </Typography>
      )
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 150,
      renderCell: (params) => (
        <Box>
          <Tooltip title="تعديل">
            <IconButton
              size="small"
              onClick={() => openEditDialog(params.row)}
              color="primary"
            >
              <Edit />
            </IconButton>
          </Tooltip>
          <Tooltip title={params.row.isActive ? 'إلغاء التفعيل' : 'تفعيل'}>
            <IconButton
              size="small"
              onClick={() => handleToggleStatus(params.row)}
              color={params.row.isActive ? 'warning' : 'success'}
            >
              {params.row.isActive ? <Cancel /> : <CheckCircle />}
            </IconButton>
          </Tooltip>
          <Tooltip title="حذف">
            <IconButton
              size="small"
              onClick={() => handleDeleteStudent(params.row._id)}
              color="error"
            >
              <Delete />
            </IconButton>
          </Tooltip>
        </Box>
      ),
      sortable: false,
      filterable: false
    }
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
          إدارة الطلاب
        </Typography>
        <Typography variant="body1" color="textSecondary">
          إضافة وإدارة طلاب المنصة
        </Typography>
      </Box>

      {/* Actions Bar */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="البحث عن طالب..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12} md={6} sx={{ textAlign: { xs: 'left', md: 'right' } }}>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={openCreateDialog}
                sx={{ fontWeight: 600 }}
              >
                إضافة طالب جديد
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Students Table */}
      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell></TableCell>
                  <TableCell>اسم الطالب</TableCell>
                  <TableCell>كود الطالب</TableCell>
                  <TableCell>الكورسات</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>آخر دخول</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {students.map((student) => (
                  <TableRow key={student._id}>
                    <TableCell>
                      <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                        {student.name.charAt(0)}
                      </Avatar>
                    </TableCell>
                    <TableCell>{student.name}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
                          {student.studentCode}
                        </Typography>
                        <Tooltip title="نسخ الكود">
                          <IconButton
                            size="small"
                            onClick={() => copyToClipboard(student.studentCode)}
                          >
                            <ContentCopy fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={student.enrolledCourses?.length || 0}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={student.isActive ? 'نشط' : 'غير نشط'}
                        color={student.isActive ? 'success' : 'error'}
                        size="small"
                        icon={student.isActive ? <CheckCircle /> : <Cancel />}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="textSecondary">
                        {student.lastLogin 
                          ? format(new Date(student.lastLogin), 'dd/MM/yyyy', { locale: ar })
                          : 'لم يدخل بعد'
                        }
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Tooltip title="تعديل">
                          <IconButton
                            size="small"
                            onClick={() => openEditDialog(student)}
                            color="primary"
                          >
                            <Edit />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={student.isActive ? 'إلغاء التفعيل' : 'تفعيل'}>
                          <IconButton
                            size="small"
                            onClick={() => handleToggleStatus(student)}
                            color={student.isActive ? 'warning' : 'success'}
                          >
                            {student.isActive ? <Cancel /> : <CheckCircle />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="حذف">
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteStudent(student._id)}
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {selectedStudent ? 'تعديل بيانات الطالب' : 'إضافة طالب جديد'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="اسم الطالب"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              sx={{ mb: 3 }}
              required
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                />
              }
              label="حساب نشط"
            />

            {!selectedStudent && (
              <Alert severity="info" sx={{ mt: 2 }}>
                سيتم إنشاء كود مكون من 6 أرقام للطالب تلقائياً
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            إلغاء
          </Button>
          <Button
            variant="contained"
            onClick={selectedStudent ? handleUpdateStudent : handleCreateStudent}
            disabled={!formData.name.trim()}
          >
            {selectedStudent ? 'تحديث' : 'إنشاء'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentsManagement;

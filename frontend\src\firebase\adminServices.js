import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './config';

// ===== إدارة الطلاب =====
export const studentManagementService = {
  // جلب جميع الطلاب
  async getAllStudents() {
    try {
      const studentsRef = collection(db, 'students');
      const q = query(studentsRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        lastLogin: doc.data().lastLogin?.toDate?.() || null
      }));
    } catch (error) {
      console.error('Error fetching students:', error);
      throw error;
    }
  },

  // جلب طالب محدد
  async getStudent(studentId) {
    try {
      const studentDoc = await getDoc(doc(db, 'students', studentId));
      if (studentDoc.exists()) {
        return {
          id: studentDoc.id,
          ...studentDoc.data(),
          createdAt: studentDoc.data().createdAt?.toDate?.() || new Date(),
          lastLogin: studentDoc.data().lastLogin?.toDate?.() || null
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching student:', error);
      throw error;
    }
  },

  // إضافة طالب جديد
  async addStudent(studentData) {
    try {
      const batch = writeBatch(db);

      // إضافة الطالب في مجموعة students
      const studentsRef = collection(db, 'students');
      const studentDocRef = doc(studentsRef);

      batch.set(studentDocRef, {
        ...studentData,
        createdAt: serverTimestamp(),
        isActive: true,
        enrolledCourses: [],
        completedCourses: [],
        certificates: [],
        totalWatchTime: 0,
        lastLogin: null
      });

      // إضافة الطالب في مجموعة profiles للتسجيل
      const profilesRef = collection(db, 'profiles');
      const profileDocRef = doc(profilesRef);

      batch.set(profileDocRef, {
        name: studentData.name,
        email: studentData.email || '',
        phone: studentData.phone || '',
        studentCode: studentData.studentCode,
        role: 'student',
        isActive: true,
        createdAt: serverTimestamp(),
        lastLogin: null,
        studentId: studentDocRef.id
      });

      await batch.commit();

      return studentDocRef.id;
    } catch (error) {
      console.error('Error adding student:', error);
      throw error;
    }
  },

  // تحديث بيانات طالب
  async updateStudent(studentId, updateData) {
    try {
      const batch = writeBatch(db);

      // تحديث في مجموعة students
      const studentRef = doc(db, 'students', studentId);
      batch.update(studentRef, {
        ...updateData,
        updatedAt: serverTimestamp()
      });

      // البحث عن الملف الشخصي وتحديثه
      if (updateData.studentCode || updateData.name || updateData.email) {
        const profilesRef = collection(db, 'profiles');
        const profileQuery = query(profilesRef, where('studentId', '==', studentId));
        const profileSnapshot = await getDocs(profileQuery);

        if (!profileSnapshot.empty) {
          const profileDoc = profileSnapshot.docs[0];
          const profileUpdateData = {};

          if (updateData.name) profileUpdateData.name = updateData.name;
          if (updateData.email) profileUpdateData.email = updateData.email;
          if (updateData.studentCode) profileUpdateData.studentCode = updateData.studentCode;
          if (updateData.isActive !== undefined) profileUpdateData.isActive = updateData.isActive;

          batch.update(profileDoc.ref, {
            ...profileUpdateData,
            updatedAt: serverTimestamp()
          });
        }
      }

      await batch.commit();
    } catch (error) {
      console.error('Error updating student:', error);
      throw error;
    }
  },

  // حذف طالب
  async deleteStudent(studentId) {
    try {
      const batch = writeBatch(db);

      // حذف من مجموعة students
      const studentRef = doc(db, 'students', studentId);
      batch.delete(studentRef);

      // البحث عن الملف الشخصي وحذفه
      const profilesRef = collection(db, 'profiles');
      const profileQuery = query(profilesRef, where('studentId', '==', studentId));
      const profileSnapshot = await getDocs(profileQuery);

      if (!profileSnapshot.empty) {
        const profileDoc = profileSnapshot.docs[0];
        batch.delete(profileDoc.ref);
      }

      await batch.commit();
    } catch (error) {
      console.error('Error deleting student:', error);
      throw error;
    }
  },

  // تفعيل/إلغاء تفعيل طالب
  async toggleStudentStatus(studentId, isActive) {
    try {
      const batch = writeBatch(db);

      // تحديث في مجموعة students
      const studentRef = doc(db, 'students', studentId);
      batch.update(studentRef, {
        isActive,
        updatedAt: serverTimestamp()
      });

      // تحديث في مجموعة profiles
      const profilesRef = collection(db, 'profiles');
      const profileQuery = query(profilesRef, where('studentId', '==', studentId));
      const profileSnapshot = await getDocs(profileQuery);

      if (!profileSnapshot.empty) {
        const profileDoc = profileSnapshot.docs[0];
        batch.update(profileDoc.ref, {
          isActive,
          updatedAt: serverTimestamp()
        });
      }

      await batch.commit();
    } catch (error) {
      console.error('Error toggling student status:', error);
      throw error;
    }
  },

  // البحث في الطلاب
  async searchStudents(searchTerm) {
    try {
      const studentsRef = collection(db, 'students');
      const snapshot = await getDocs(studentsRef);

      const students = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return students.filter(student =>
        student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.phone?.includes(searchTerm)
      );
    } catch (error) {
      console.error('Error searching students:', error);
      throw error;
    }
  },

  // جلب إحصائيات الطلاب
  async getStudentStats() {
    try {
      const studentsRef = collection(db, 'students');
      const snapshot = await getDocs(studentsRef);

      const students = snapshot.docs.map(doc => doc.data());
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      return {
        total: students.length,
        active: students.filter(s => s.isActive).length,
        newThisWeek: students.filter(s =>
          s.createdAt?.toDate?.() > weekAgo
        ).length,
        newThisMonth: students.filter(s =>
          s.createdAt?.toDate?.() > monthAgo
        ).length,
        activeThisWeek: students.filter(s =>
          s.lastLogin?.toDate?.() > weekAgo
        ).length
      };
    } catch (error) {
      console.error('Error fetching student stats:', error);
      throw error;
    }
  }
};

// ===== إدارة الكورسات =====
export const courseManagementService = {
  // جلب جميع الكورسات
  async getAllCourses() {
    try {
      const coursesRef = collection(db, 'courses');
      const q = query(coursesRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('Error fetching courses:', error);
      throw error;
    }
  },

  // إضافة كورس جديد
  async addCourse(courseData) {
    try {
      const coursesRef = collection(db, 'courses');
      const docRef = await addDoc(coursesRef, {
        ...courseData,
        createdAt: serverTimestamp(),
        isActive: true,
        enrolledStudents: 0,
        completedStudents: 0,
        totalViews: 0,
        averageRating: 0,
        ratings: []
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding course:', error);
      throw error;
    }
  },

  // تحديث كورس
  async updateCourse(courseId, updateData) {
    try {
      const courseRef = doc(db, 'courses', courseId);
      await updateDoc(courseRef, {
        ...updateData,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating course:', error);
      throw error;
    }
  },

  // حذف كورس
  async deleteCourse(courseId) {
    try {
      await deleteDoc(doc(db, 'courses', courseId));
    } catch (error) {
      console.error('Error deleting course:', error);
      throw error;
    }
  },

  // جلب إحصائيات الكورسات
  async getCourseStats() {
    try {
      const coursesRef = collection(db, 'courses');
      const snapshot = await getDocs(coursesRef);

      const courses = snapshot.docs.map(doc => doc.data());

      return {
        total: courses.length,
        active: courses.filter(c => c.isActive).length,
        totalEnrollments: courses.reduce((sum, c) => sum + (c.enrolledStudents || 0), 0),
        totalCompletions: courses.reduce((sum, c) => sum + (c.completedStudents || 0), 0),
        averageRating: courses.length > 0
          ? courses.reduce((sum, c) => sum + (c.averageRating || 0), 0) / courses.length
          : 0,
        mostPopular: courses.sort((a, b) => (b.enrolledStudents || 0) - (a.enrolledStudents || 0))[0]?.title || 'لا يوجد'
      };
    } catch (error) {
      console.error('Error fetching course stats:', error);
      throw error;
    }
  }
};

// ===== إدارة الأسئلة الشائعة =====
export const faqManagementService = {
  // جلب جميع الأسئلة الشائعة
  async getAllFAQs() {
    try {
      const faqsRef = collection(db, 'faqs');
      const q = query(faqsRef, orderBy('priority', 'asc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('Error fetching FAQs:', error);
      throw error;
    }
  },

  // إضافة سؤال جديد
  async addFAQ(faqData) {
    try {
      const faqsRef = collection(db, 'faqs');
      const docRef = await addDoc(faqsRef, {
        ...faqData,
        createdAt: serverTimestamp(),
        isActive: true
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding FAQ:', error);
      throw error;
    }
  },

  // تحديث سؤال
  async updateFAQ(faqId, updateData) {
    try {
      const faqRef = doc(db, 'faqs', faqId);
      await updateDoc(faqRef, {
        ...updateData,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating FAQ:', error);
      throw error;
    }
  },

  // حذف سؤال
  async deleteFAQ(faqId) {
    try {
      await deleteDoc(doc(db, 'faqs', faqId));
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      throw error;
    }
  },

  // تفعيل/إلغاء تفعيل سؤال
  async toggleFAQStatus(faqId, isActive) {
    try {
      const faqRef = doc(db, 'faqs', faqId);
      await updateDoc(faqRef, {
        isActive,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error toggling FAQ status:', error);
      throw error;
    }
  }
};

// ===== إدارة الشهادات =====
export const certificateManagementService = {
  // جلب جميع الشهادات
  async getAllCertificates() {
    try {
      const certificatesRef = collection(db, 'certificates');
      const q = query(certificatesRef, orderBy('issuedAt', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        issuedAt: doc.data().issuedAt?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('Error fetching certificates:', error);
      throw error;
    }
  },

  // إصدار شهادة جديدة
  async issueCertificate(certificateData) {
    try {
      const certificatesRef = collection(db, 'certificates');
      const docRef = await addDoc(certificatesRef, {
        ...certificateData,
        issuedAt: serverTimestamp(),
        certificateNumber: `CERT-${Date.now()}`,
        isValid: true
      });

      return docRef.id;
    } catch (error) {
      console.error('Error issuing certificate:', error);
      throw error;
    }
  },

  // إلغاء شهادة
  async revokeCertificate(certificateId) {
    try {
      const certificateRef = doc(db, 'certificates', certificateId);
      await updateDoc(certificateRef, {
        isValid: false,
        revokedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error revoking certificate:', error);
      throw error;
    }
  },

  // جلب إحصائيات الشهادات
  async getCertificateStats() {
    try {
      const certificatesRef = collection(db, 'certificates');
      const snapshot = await getDocs(certificatesRef);

      const certificates = snapshot.docs.map(doc => doc.data());
      const now = new Date();
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      return {
        total: certificates.length,
        valid: certificates.filter(c => c.isValid).length,
        revoked: certificates.filter(c => !c.isValid).length,
        thisMonth: certificates.filter(c =>
          c.issuedAt?.toDate?.() > monthAgo
        ).length
      };
    } catch (error) {
      console.error('Error fetching certificate stats:', error);
      throw error;
    }
  }
};

// ===== إدارة المحادثات =====
export const chatManagementService = {
  // جلب جميع المحادثات
  async getAllConversations() {
    try {
      const conversationsRef = collection(db, 'conversations');
      const q = query(conversationsRef, orderBy('lastMessageTime', 'desc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        lastMessageTime: doc.data().lastMessageTime?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('Error fetching conversations:', error);
      throw error;
    }
  },

  // جلب رسائل محادثة محددة
  async getMessages(conversationId) {
    try {
      const messagesRef = collection(db, 'conversations', conversationId, 'messages');
      const q = query(messagesRef, orderBy('timestamp', 'asc'));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  },

  // إرسال رسالة
  async sendMessage(conversationId, messageData) {
    try {
      const messagesRef = collection(db, 'conversations', conversationId, 'messages');
      await addDoc(messagesRef, {
        ...messageData,
        timestamp: serverTimestamp(),
        isRead: false
      });

      // تحديث آخر رسالة في المحادثة
      const conversationRef = doc(db, 'conversations', conversationId);
      await updateDoc(conversationRef, {
        lastMessage: messageData.message,
        lastMessageTime: serverTimestamp(),
        lastSenderId: messageData.senderId
      });
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },

  // إرسال رسالة جماعية
  async sendBroadcastMessage(messageData) {
    try {
      const studentsRef = collection(db, 'students');
      const studentsSnapshot = await getDocs(studentsRef);

      const batch = writeBatch(db);

      studentsSnapshot.docs.forEach(studentDoc => {
        const conversationRef = doc(db, 'conversations', `admin_${studentDoc.id}`);
        const messageRef = doc(collection(conversationRef, 'messages'));

        batch.set(messageRef, {
          ...messageData,
          timestamp: serverTimestamp(),
          isRead: false,
          isBroadcast: true
        });

        batch.set(conversationRef, {
          studentId: studentDoc.id,
          studentName: studentDoc.data().name,
          lastMessage: messageData.message,
          lastMessageTime: serverTimestamp(),
          lastSenderId: 'admin',
          unreadCount: 1
        }, { merge: true });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error sending broadcast message:', error);
      throw error;
    }
  }
};

// ===== خدمة التحليلات والإحصائيات =====
export const analyticsService = {
  // جلب الإحصائيات العامة
  async getOverviewStats() {
    try {
      const [studentsStats, coursesStats, certificatesStats] = await Promise.all([
        studentManagementService.getStudentStats(),
        courseManagementService.getCourseStats(),
        certificateManagementService.getCertificateStats()
      ]);

      return {
        students: studentsStats,
        courses: coursesStats,
        certificates: certificatesStats,
        overview: {
          totalStudents: studentsStats.total,
          activeStudents: studentsStats.active,
          totalCourses: coursesStats.total,
          totalCertificates: certificatesStats.total,
          averageProgress: await this.calculateAverageProgress()
        }
      };
    } catch (error) {
      console.error('Error fetching overview stats:', error);
      throw error;
    }
  },

  // حساب متوسط التقدم
  async calculateAverageProgress() {
    try {
      const studentsRef = collection(db, 'students');
      const snapshot = await getDocs(studentsRef);

      if (snapshot.empty) return 0;

      let totalProgress = 0;
      let studentCount = 0;

      snapshot.docs.forEach(doc => {
        const student = doc.data();
        if (student.enrolledCourses && student.enrolledCourses.length > 0) {
          const progress = student.enrolledCourses.reduce((sum, course) =>
            sum + (course.progress || 0), 0) / student.enrolledCourses.length;
          totalProgress += progress;
          studentCount++;
        }
      });

      return studentCount > 0 ? Math.round(totalProgress / studentCount) : 0;
    } catch (error) {
      console.error('Error calculating average progress:', error);
      return 0;
    }
  },

  // جلب النشاط الأخير
  async getRecentActivity(limit = 10) {
    try {
      const activitiesRef = collection(db, 'activities');
      const q = query(activitiesRef, orderBy('timestamp', 'desc'), limit(limit));
      const snapshot = await getDocs(q);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate?.() || new Date()
      }));
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      return [];
    }
  },

  // تسجيل نشاط جديد
  async logActivity(activityData) {
    try {
      const activitiesRef = collection(db, 'activities');
      await addDoc(activitiesRef, {
        ...activityData,
        timestamp: serverTimestamp()
      });
    } catch (error) {
      console.error('Error logging activity:', error);
    }
  },

  // جلب إحصائيات مفصلة للطلاب
  async getDetailedStudentStats(dateRange = 30) {
    try {
      const studentsRef = collection(db, 'students');
      const snapshot = await getDocs(studentsRef);

      const students = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate?.() || new Date(),
        lastLogin: doc.data().lastLogin?.toDate?.() || null
      }));

      const now = new Date();
      const rangeStart = new Date(now.getTime() - dateRange * 24 * 60 * 60 * 1000);

      return {
        totalStudents: students.length,
        activeStudents: students.filter(s => s.isActive).length,
        newRegistrations: students.filter(s => s.createdAt > rangeStart).length,
        activeInRange: students.filter(s => s.lastLogin && s.lastLogin > rangeStart).length,
        completionRate: this.calculateCompletionRate(students),
        averageTimeSpent: this.calculateAverageTimeSpent(students),
        topPerformers: this.getTopPerformers(students, 5)
      };
    } catch (error) {
      console.error('Error fetching detailed student stats:', error);
      throw error;
    }
  },

  // حساب معدل الإكمال
  calculateCompletionRate(students) {
    const studentsWithCourses = students.filter(s =>
      s.enrolledCourses && s.enrolledCourses.length > 0
    );

    if (studentsWithCourses.length === 0) return 0;

    const totalCompletions = studentsWithCourses.reduce((sum, student) =>
      sum + (student.completedCourses?.length || 0), 0
    );

    const totalEnrollments = studentsWithCourses.reduce((sum, student) =>
      sum + (student.enrolledCourses?.length || 0), 0
    );

    return totalEnrollments > 0 ? Math.round((totalCompletions / totalEnrollments) * 100) : 0;
  },

  // حساب متوسط الوقت المقضي
  calculateAverageTimeSpent(students) {
    const studentsWithTime = students.filter(s => s.totalWatchTime && s.totalWatchTime > 0);

    if (studentsWithTime.length === 0) return 0;

    const totalTime = studentsWithTime.reduce((sum, student) =>
      sum + (student.totalWatchTime || 0), 0
    );

    return Math.round(totalTime / studentsWithTime.length);
  },

  // جلب أفضل الطلاب أداءً
  getTopPerformers(students, limit = 5) {
    return students
      .filter(s => s.completedCourses && s.completedCourses.length > 0)
      .sort((a, b) => (b.completedCourses?.length || 0) - (a.completedCourses?.length || 0))
      .slice(0, limit)
      .map(student => ({
        id: student.id,
        name: student.name,
        completedCourses: student.completedCourses?.length || 0,
        totalWatchTime: student.totalWatchTime || 0
      }));
  },

  // جلب إحصائيات مفصلة للكورسات
  async getDetailedCourseStats() {
    try {
      const coursesRef = collection(db, 'courses');
      const snapshot = await getDocs(coursesRef);

      const courses = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return {
        totalCourses: courses.length,
        activeCourses: courses.filter(c => c.isActive).length,
        totalEnrollments: courses.reduce((sum, c) => sum + (c.enrolledStudents || 0), 0),
        totalCompletions: courses.reduce((sum, c) => sum + (c.completedStudents || 0), 0),
        averageRating: courses.length > 0
          ? courses.reduce((sum, c) => sum + (c.averageRating || 0), 0) / courses.length
          : 0,
        mostPopular: courses.sort((a, b) => (b.enrolledStudents || 0) - (a.enrolledStudents || 0))[0],
        highestRated: courses.sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0))[0],
        mostCompleted: courses.sort((a, b) => (b.completedStudents || 0) - (a.completedStudents || 0))[0]
      };
    } catch (error) {
      console.error('Error fetching detailed course stats:', error);
      throw error;
    }
  }
};

// ===== خدمة إدارة الملف الشخصي للمدير =====
export const adminProfileService = {
  // جلب بيانات المدير
  async getAdminProfile(adminId) {
    try {
      const adminDoc = await getDoc(doc(db, 'admins', adminId));
      if (adminDoc.exists()) {
        return {
          id: adminDoc.id,
          ...adminDoc.data(),
          createdAt: adminDoc.data().createdAt?.toDate?.() || new Date(),
          lastLogin: adminDoc.data().lastLogin?.toDate?.() || null
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching admin profile:', error);
      throw error;
    }
  },

  // تحديث بيانات المدير
  async updateAdminProfile(adminId, updateData) {
    try {
      const adminRef = doc(db, 'admins', adminId);
      await updateDoc(adminRef, {
        ...updateData,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating admin profile:', error);
      throw error;
    }
  },

  // تحديث آخر تسجيل دخول
  async updateLastLogin(adminId) {
    try {
      const adminRef = doc(db, 'admins', adminId);
      await updateDoc(adminRef, {
        lastLogin: serverTimestamp()
      });
    } catch (error) {
      console.error('Error updating last login:', error);
      throw error;
    }
  }
};
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Avatar,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Paper,
  LinearProgress,
  CircularProgress
} from '@mui/material';
import {
  PlayArrow,
  School,
  VideoLibrary,
  WorkspacePremium,
  Logout,
  AccountCircle,
  ExpandMore,
  Edit,
  Save,
  Cancel,
  Star,
  TrendingUp,
  Support,
  ExitToApp,
  Help,
  Close
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import CourseViewer from './CourseViewer';
import ContactAdmin from './ContactAdmin';
import SmartAssistant from './SmartAssistant';
import ProfileDialog from './ProfileDialog';
import FAQDialog from './FAQDialog';
import { getUserProfile } from '../firebase/profileService';
import { studentDataService } from '../firebase/studentDataService';
import toast from 'react-hot-toast';

const StudentDashboard = () => {
  const { user, logout, updateUser } = useAuth();
  const [anchorEl, setAnchorEl] = useState(null);
  const [courses, setCourses] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [openContactDialog, setOpenContactDialog] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showFAQ, setShowFAQ] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    enrolledCourses: 0,
    completedVideos: 0,
    totalVideos: 0,
    certificates: 0
  });

  useEffect(() => {
    if (user?.id) {
      loadUserProfile();
      fetchStudentData();
    }
  }, [user?.id]);

  const loadUserProfile = async () => {
    if (user?.id) {
      try {
        console.log('📥 تحميل الملف الشخصي من Firebase...', user.id);
        const profile = await getUserProfile(user.id);
        if (profile && (profile.name !== user.name || profile.email !== user.email || profile.phone !== user.phone)) {
          // تحديث بيانات المستخدم إذا كانت مختلفة
          const updatedUser = { ...user, ...profile };
          updateUser(updatedUser);
          console.log('✅ تم تحديث بيانات المستخدم من Firebase:', updatedUser);
        }
      } catch (error) {
        console.log('⚠️ فشل تحميل الملف الشخصي من Firebase:', error.message);
      }
    }
  };

  const fetchStudentData = async () => {
    try {
      setLoading(true);
      console.log('📥 تحميل البيانات من Firebase...', user?.id);

      if (!user?.id) {
        console.log('⚠️ لا يوجد معرف مستخدم');
        setLoading(false);
        return;
      }

      // جلب بيانات الطالب من Firebase
      const studentData = await studentDataService.getStudentData(user.id);

      if (studentData) {
        console.log('✅ تم جلب بيانات الطالب:', studentData);

        // تحديث الإحصائيات
        setStats({
          enrolledCourses: studentData.enrolledCourses?.length || 0,
          completedVideos: studentData.progress?.completedVideos || 0,
          totalVideos: studentData.progress?.totalVideos || 0,
          certificates: studentData.certificates?.length || 0
        });

        // تحديث الدورات
        if (studentData.enrolledCourses && studentData.enrolledCourses.length > 0) {
          setCourses(studentData.enrolledCourses);
        } else {
          // إنشاء دورات افتراضية إذا لم توجد
          await createDefaultCourses();
        }
      } else {
        // إنشاء بيانات افتراضية للطالب الجديد
        console.log('📝 إنشاء بيانات افتراضية للطالب...');
        await createDefaultCourses();
      }
    } catch (error) {
      console.error('❌ خطأ في تحميل بيانات الطالب:', error);
      // استخدام بيانات افتراضية في حالة الخطأ
      await createDefaultCourses();
    } finally {
      setLoading(false);
    }
  };

  const createDefaultCourses = async () => {
    try {
      const defaultCourses = [
        {
          id: 'course1',
          title: 'أساسيات التسويق الرقمي',
          description: 'تعلم أساسيات التسويق الرقمي من الصفر حتى الاحتراف',
          instructor: 'علاء عبد الحميد',
          rating: 4.8,
          totalVideos: 12,
          completedVideos: 8,
          progress: 67,
          isEnrolled: true,
          enrolledAt: new Date().toISOString(),
          lastAccessed: new Date().toISOString()
        },
        {
          id: 'course2',
          title: 'إدارة وسائل التواصل الاجتماعي',
          description: 'كيفية إدارة حسابات التواصل الاجتماعي بفعالية واحترافية',
          instructor: 'علاء عبد الحميد',
          rating: 4.9,
          totalVideos: 10,
          completedVideos: 3,
          progress: 30,
          isEnrolled: true,
          enrolledAt: new Date().toISOString(),
          lastAccessed: new Date().toISOString()
        }
      ];

      setCourses(defaultCourses);

      // حفظ البيانات في Firebase
      if (user?.id) {
        const studentData = {
          userId: user.id,
          enrolledCourses: defaultCourses,
          progress: {
            completedVideos: 11,
            totalVideos: 22,
            overallProgress: 50
          },
          certificates: [],
          lastLogin: new Date().toISOString(),
          createdAt: new Date().toISOString()
        };

        await studentDataService.saveStudentData(user.id, studentData);
        console.log('✅ تم حفظ البيانات الافتراضية في Firebase');
      }

      setStats({
        enrolledCourses: defaultCourses.length,
        completedVideos: 11,
        totalVideos: 22,
        certificates: 0
      });
    } catch (error) {
      console.error('❌ خطأ في إنشاء البيانات الافتراضية:', error);
    }
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleMenuClose();
  };

  const getProgressColor = (progress) => {
    if (progress === 0) return 'error';
    if (progress < 50) return 'warning';
    if (progress < 100) return 'info';
    return 'success';
  };

  const handleCourseClick = (course) => {
    setSelectedCourse(course);
    console.log('تم النقر على الدورة:', course.title);
  };

  const handleBackToDashboard = () => {
    setSelectedCourse(null);
  };

  if (selectedCourse) {
    return (
      <CourseViewer
        course={selectedCourse}
        onBack={handleBackToDashboard}
        user={user}
      />
    );
  }

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        bgcolor: '#f5f5f5'
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={60} sx={{ color: '#0000FF', mb: 2 }} />
          <Typography variant="h6" sx={{ color: '#0000FF' }}>
            جاري تحميل بياناتك...
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{
      flexGrow: 1,
      bgcolor: '#f5f5f5',
      minHeight: '100vh',
      direction: 'rtl'
    }}>
      {/* شريط التنقل العلوي */}
      <AppBar position="static" sx={{
        background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
        boxShadow: '0 4px 20px rgba(0,0,255,0.3)'
      }}>
        <Toolbar sx={{
          justifyContent: 'space-between',
          px: { xs: 2, sm: 3 }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <School sx={{
              fontSize: { xs: '1.8rem', sm: '2.2rem' },
              color: '#FFD700'
            }} />
            <Box>
              <Typography variant="h6" sx={{
                fontWeight: 'bold',
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}>
                SKILLS WORLD ACADEMY
              </Typography>
              <Typography variant="caption" sx={{
                opacity: 0.9,
                fontSize: { xs: '0.7rem', sm: '0.75rem' }
              }}>
                ALAA ABD HAMIED
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" sx={{
              display: { xs: 'none', sm: 'block' },
              mr: 2
            }}>
              مرحباً، {user?.name}
            </Typography>

            <Button
              color="inherit"
              onClick={handleLogout}
              startIcon={<ExitToApp />}
              sx={{
                display: { xs: 'none', sm: 'flex' },
                borderRadius: 2,
                px: 2,
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.1)'
                }
              }}
            >
              تسجيل الخروج
            </Button>

            <IconButton
              size="large"
              edge="end"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenuOpen}
              color="inherit"
              sx={{
                p: { xs: 0.5, sm: 1 },
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' }
              }}
            >
              <Avatar sx={{
                width: { xs: 28, sm: 32 },
                height: { xs: 28, sm: 32 },
                bgcolor: '#FFD700',
                color: '#000',
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }}>
                {user?.name?.charAt(0)}
              </Avatar>
            </IconButton>

            <Menu
              id="menu-appbar"
              anchorEl={anchorEl}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              keepMounted
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              sx={{
                '& .MuiPaper-root': {
                  borderRadius: 2,
                  minWidth: 180,
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
                }
              }}
            >
              <MenuItem onClick={() => { setShowProfile(true); handleMenuClose(); }}>
                <AccountCircle sx={{ mr: 1, color: '#0000FF' }} />
                الملف الشخصي
              </MenuItem>
              <MenuItem onClick={() => { setShowFAQ(true); handleMenuClose(); }}>
                <Help sx={{ mr: 1, color: '#0000FF' }} />
                الأسئلة الشائعة
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <ExitToApp sx={{ mr: 1, color: '#f44336' }} />
                تسجيل الخروج
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      <Box sx={{
        p: { xs: 1, sm: 2, md: 3 },
        bgcolor: '#f5f5f5',
        minHeight: 'calc(100vh - 64px)'
      }}>
        {/* إحصائيات سريعة */}
        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 3, sm: 4 } }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
              color: 'white',
              height: '100%',
              minHeight: { xs: '120px', sm: '140px' }
            }}>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' },
                  gap: { xs: 1, sm: 2 }
                }}>
                  <School sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem' },
                    color: '#FFD700',
                    order: { xs: 2, sm: 1 }
                  }} />
                  <Box sx={{ order: { xs: 1, sm: 2 } }}>
                    <Typography variant="h4" sx={{
                      fontWeight: 'bold',
                      fontSize: { xs: '1.8rem', sm: '2.125rem' }
                    }}>
                      {stats.enrolledCourses}
                    </Typography>
                    <Typography variant="body2" sx={{
                      opacity: 0.9,
                      fontSize: { xs: '0.8rem', sm: '0.875rem' }
                    }}>
                      الدورات المسجلة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              background: 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)',
              color: 'white',
              height: '100%',
              minHeight: { xs: '120px', sm: '140px' }
            }}>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' },
                  gap: { xs: 1, sm: 2 }
                }}>
                  <VideoLibrary sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem' },
                    color: '#FFD700',
                    order: { xs: 2, sm: 1 }
                  }} />
                  <Box sx={{ order: { xs: 1, sm: 2 } }}>
                    <Typography variant="h4" sx={{
                      fontWeight: 'bold',
                      fontSize: { xs: '1.8rem', sm: '2.125rem' }
                    }}>
                      {stats.completedVideos}/{stats.totalVideos}
                    </Typography>
                    <Typography variant="body2" sx={{
                      opacity: 0.9,
                      fontSize: { xs: '0.8rem', sm: '0.875rem' }
                    }}>
                      الفيديوهات المكتملة
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              background: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)',
              color: 'white',
              height: '100%',
              minHeight: { xs: '120px', sm: '140px' }
            }}>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' },
                  gap: { xs: 1, sm: 2 }
                }}>
                  <TrendingUp sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem' },
                    color: '#FFD700',
                    order: { xs: 2, sm: 1 }
                  }} />
                  <Box sx={{ order: { xs: 1, sm: 2 } }}>
                    <Typography variant="h4" sx={{
                      fontWeight: 'bold',
                      fontSize: { xs: '1.8rem', sm: '2.125rem' }
                    }}>
                      {Math.round((stats.completedVideos / stats.totalVideos) * 100) || 0}%
                    </Typography>
                    <Typography variant="body2" sx={{
                      opacity: 0.9,
                      fontSize: { xs: '0.8rem', sm: '0.875rem' }
                    }}>
                      نسبة الإنجاز
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              background: 'linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%)',
              color: 'white',
              height: '100%',
              minHeight: { xs: '120px', sm: '140px' }
            }}>
              <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: { xs: 'column', sm: 'row' },
                  textAlign: { xs: 'center', sm: 'left' },
                  gap: { xs: 1, sm: 2 }
                }}>
                  <WorkspacePremium sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem' },
                    color: '#FFD700',
                    order: { xs: 2, sm: 1 }
                  }} />
                  <Box sx={{ order: { xs: 1, sm: 2 } }}>
                    <Typography variant="h4" sx={{
                      fontWeight: 'bold',
                      fontSize: { xs: '1.8rem', sm: '2.125rem' }
                    }}>
                      {stats.certificates}
                    </Typography>
                    <Typography variant="body2" sx={{
                      opacity: 0.9,
                      fontSize: { xs: '0.8rem', sm: '0.875rem' }
                    }}>
                      الشهادات
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* الدورات */}
        <Typography
          variant="h5"
          sx={{
            fontWeight: 'bold',
            mb: { xs: 2, sm: 3 },
            fontSize: { xs: '1.5rem', sm: '1.75rem' },
            color: '#0000FF',
            textAlign: { xs: 'center', sm: 'left' }
          }}
        >
          دوراتي
        </Typography>

        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {courses.map((course) => (
            <Grid item xs={12} sm={6} md={6} key={course.id}>
              <Card sx={{
                height: '100%',
                borderRadius: 3,
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 16px 48px rgba(0,0,0,0.15)'
                }
              }}>
                <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 'bold',
                        mb: 1,
                        fontSize: { xs: '1.1rem', sm: '1.25rem' },
                        color: '#0000FF'
                      }}
                    >
                      {course.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="textSecondary"
                      sx={{
                        mb: 2,
                        fontSize: { xs: '0.85rem', sm: '0.875rem' },
                        lineHeight: 1.5
                      }}
                    >
                      {course.description}
                    </Typography>
                  </Box>

                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 2,
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: { xs: 1, sm: 0 }
                  }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#666',
                        fontSize: { xs: '0.8rem', sm: '0.875rem' }
                      }}
                    >
                      المدرب: {course.instructor}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Star sx={{ color: '#FFD700', fontSize: '1rem' }} />
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 'bold',
                          fontSize: { xs: '0.8rem', sm: '0.875rem' }
                        }}
                      >
                        {course.rating}
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      mb: 1
                    }}>
                      <Typography
                        variant="body2"
                        sx={{
                          fontSize: { xs: '0.8rem', sm: '0.875rem' }
                        }}
                      >
                        التقدم: {course.completedVideos}/{course.totalVideos} فيديو
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 'bold',
                          color: getProgressColor(course.progress),
                          fontSize: { xs: '0.8rem', sm: '0.875rem' }
                        }}
                      >
                        {course.progress}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={course.progress}
                      color={getProgressColor(course.progress)}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: '#f0f0f0'
                      }}
                    />
                  </Box>

                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<PlayArrow />}
                    onClick={() => handleCourseClick(course)}
                    sx={{
                      background: 'linear-gradient(135deg, #0000FF 0%, #4169E1 100%)',
                      borderRadius: 2,
                      py: { xs: 1, sm: 1.5 },
                      fontSize: { xs: '0.9rem', sm: '1rem' },
                      '&:hover': {
                        background: 'linear-gradient(135deg, #0000CC 0%, #3557C1 100%)'
                      }
                    }}
                  >
                    متابعة التعلم
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* مكون التواصل مع المدير */}
        <ContactAdmin
          open={openContactDialog}
          onClose={() => setOpenContactDialog(false)}
        />

        {/* المساعد الذكي */}
        <SmartAssistant language="ar" />
      </Box>

      {/* نوافذ الحوار */}
      <ProfileDialog
        open={showProfile}
        onClose={() => setShowProfile(false)}
        user={user}
      />

      <FAQDialog
        open={showFAQ}
        onClose={() => setShowFAQ(false)}
      />
    </Box>
  );
};

export default StudentDashboard;
